import '../styles/middle.css'
import { useState } from 'react'

import Image1 from '../assets/images/image-1.jpeg'
import Image2 from '../assets/images/image-2.jpeg'
import Image3 from '../assets/images/image-3.jpeg'
import Image4 from '../assets/images/image-4.jpeg'
import Video1 from '../assets/video/video-1.mp4'
import Banner1 from '../assets/banner/banner-img1.png'

function Middle() {
  const [currentProduct, setCurrentProduct] = useState(0);

  const products = [
    {
      id: 1,
      image: Image1,
      name: "Porsche Design Chronotimer Series 1",
      model: "PD-G16-2025",
      price: "$2,500",
      originalPrice: "$3,200",
      discount: "22% OFF",
      description: "Premium luxury timepiece featuring Swiss automatic movement with 42-hour power reserve. Crafted with titanium case and sapphire crystal glass for ultimate durability.",
      features: [
        "Swiss Automatic Movement",
        "Titanium Case Construction",
        "Sapphire Crystal Glass",
        "Water Resistant 300m",
        "Anti-Magnetic Protection",
        "Luminous Hands & Markers"
      ],
      specifications: {
        caseSize: "42mm",
        thickness: "12mm",
        material: "Grade 5 Titanium",
        movement: "Swiss ETA 2824-2",
        waterResistance: "300m/1000ft",
        warranty: "5 Years International"
      },
      mfgDate: "12/02/2025",
      availability: "In Stock",
      rating: "4.9/5",
      bgClass: "product1"
    },
    {
      id: 2,
      image: Image2,
      name: "Porsche Design Timemaster Elite",
      model: "PD-T26-2024",
      price: "$3,500",
      originalPrice: "$4,200",
      discount: "17% OFF",
      description: "Sophisticated chronograph with carbon fiber bezel and ceramic case back. Features advanced GMT function and perpetual calendar for the modern professional.",
      features: [
        "Chronograph Function",
        "GMT Dual Time Zone",
        "Carbon Fiber Bezel",
        "Ceramic Case Back",
        "Perpetual Calendar",
        "Tachymeter Scale"
      ],
      specifications: {
        caseSize: "44mm",
        thickness: "14mm",
        material: "Stainless Steel & Carbon",
        movement: "Swiss Valjoux 7750",
        waterResistance: "200m/660ft",
        warranty: "3 Years International"
      },
      mfgDate: "12/02/2024",
      availability: "Limited Edition",
      rating: "4.8/5",
      bgClass: "product2"
    },
    {
      id: 3,
      image: Image3,
      name: "Porsche Design Racing Collection",
      model: "PD-G12-2024",
      price: "$2,800",
      originalPrice: "$3,400",
      discount: "18% OFF",
      description: "Inspired by motorsport heritage, this timepiece features racing-inspired design elements with high-performance materials and precision engineering.",
      features: [
        "Racing Inspired Design",
        "Lightweight Aluminum",
        "Quick Release Strap",
        "Shock Resistant",
        "Date Display",
        "Luminous Coating"
      ],
      specifications: {
        caseSize: "40mm",
        thickness: "11mm",
        material: "Aluminum Alloy",
        movement: "Swiss Quartz",
        waterResistance: "100m/330ft",
        warranty: "2 Years International"
      },
      mfgDate: "12/01/2024",
      availability: "In Stock",
      rating: "4.7/5",
      bgClass: "product3"
    },
    {
      id: 4,
      image: Image4,
      name: "Porsche Design Urban Explorer",
      model: "PD-U16-2024",
      price: "$2,900",
      originalPrice: "$3,600",
      discount: "19% OFF",
      description: "Urban lifestyle watch with smart connectivity features. Combines traditional craftsmanship with modern technology for the contemporary explorer.",
      features: [
        "Smart Connectivity",
        "GPS Navigation",
        "Heart Rate Monitor",
        "Wireless Charging",
        "Multiple Time Zones",
        "Activity Tracking"
      ],
      specifications: {
        caseSize: "43mm",
        thickness: "13mm",
        material: "Stainless Steel",
        movement: "Hybrid Smart Movement",
        waterResistance: "150m/500ft",
        warranty: "2 Years + 1 Year Tech"
      },
      mfgDate: "22/08/2024",
      availability: "Pre-Order",
      rating: "4.6/5",
      bgClass: "product4"
    },
    {
      id: 5,
      image: Image1,
      name: "Porsche Design Heritage Classic",
      model: "PD-R9-2023",
      price: "$4,500",
      originalPrice: "$5,800",
      discount: "22% OFF",
      description: "Vintage-inspired masterpiece celebrating Porsche's rich heritage. Hand-assembled with premium materials and traditional watchmaking techniques.",
      features: [
        "Hand-Assembled Movement",
        "18K Gold Accents",
        "Leather Strap Premium",
        "Limited Production",
        "Vintage Inspired",
        "Collector's Edition"
      ],
      specifications: {
        caseSize: "41mm",
        thickness: "12.5mm",
        material: "18K Gold & Steel",
        movement: "Swiss Manual Wind",
        waterResistance: "50m/165ft",
        warranty: "Lifetime Service"
      },
      mfgDate: "28/12/2023",
      availability: "Collector's Item",
      rating: "5.0/5",
      bgClass: "product5"
    }
  ];

  const handleProductChange = (index) => {
    setCurrentProduct(index);
  };

  return (
    <>

      <div className="parent">
        <div className="div1">
          <img src={Image1} alt="Image 1" className="Image1" />
        </div>
        <div className="div2">
          <img src={Image2} alt="Image 2" className="Image2" />
        </div>
        <div className="div3">
          <video src={Video1} autoPlay loop muted className="Video1" />
        </div>
        <div className="div4">
          <img src={Image3} alt="Image 3" className="Image3" />
        </div>
        <div className="div5">
          <img src={Image4} alt="Image 4" className="Image4" />
        </div>
      </div>

      <div className="marquee-tilted-wrapper">
        <div className="marquee-wrapper">
          <div className="marquee-content">
            <span> Exclusive Offers & Discounts </span>
            <span> Limited Time Deals </span>
            <span> Save Big Today</span>
            <span> Shop Now & Save </span>
            <span> Special Festive Discounts </span>
          </div>
        </div>
      </div>

      <div className='banner'>
        <img src={Banner1} alt="Banner 1" id="Banner1" />
        <h1 id='banner-heading'>Shop Latest Models</h1>
        <p id='banner-subtittle'>Best Watches In The Segment</p>
        <button id="banner-button" className="button">
          <span id='button-text'>Explore</span>
        </button>

      </div>

      <div className='trending'>
        <div className='headings'>
          <h1 id='main-heading'>TRENDING Now</h1>
          <p id='subtittle'>Shop Now The Best Watches In The Market</p>
        </div>
        <div className="trending-images">
          <div className="trend1">
            <img src={Image1} alt="Image 1" className="Image1" />
          </div>
          <div className="trend2">
            <img src={Image2} alt="Image 2" className="Image2" />
          </div>
          <div className="trend3">
            <video src={Video1} autoPlay loop muted className="Video1" />
          </div>
          <div className="trend4">
            <img src={Image3} alt="Image 3" className="Image3" />
          </div>
          <div className="trend5">
            <img src={Image4} alt="Image 4" className="Image4" />
          </div>
        </div>
      </div>


      {/* New Products Showcase Section */}
      <section className='premium-showcase'>
        <div className='showcase-header'>
          <h1 className='showcase-title'>PREMIUM COLLECTION</h1>
          <p className='showcase-subtitle'>Discover Luxury Timepieces</p>
        </div>

        <div className='product-display-area'>
          <div className={`product-card ${products[currentProduct].bgClass}`}>

            {/* Left Side - Product Image */}
            <div className='product-visual'>
              <div className='image-frame'>
                <img
                  src={products[currentProduct].image}
                  alt={products[currentProduct].name}
                  className='product-image'
                />
                <div className='image-overlay'></div>
              </div>

              {/* Navigation Controls */}
              <div className='navigation-controls'>
                {products.map((_, index) => (
                  <button
                    key={index}
                    className={`nav-dot ${currentProduct === index ? 'active' : ''}`}
                    onClick={() => handleProductChange(index)}
                  >
                    <span>{index + 1}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Right Side - Product Details */}
            <div className='product-details'>

              {/* Product Header */}
              <div className='detail-header'>
                <h2 className='product-title'>{products[currentProduct].name}</h2>
                <div className='product-code'>Model: {products[currentProduct].model}</div>

                <div className='price-section'>
                  <span className='price-current'>{products[currentProduct].price}</span>
                  <span className='price-original'>{products[currentProduct].originalPrice}</span>
                  <div className='discount-badge'>{products[currentProduct].discount}</div>
                </div>

                <div className='status-bar'>
                  <span className='stock-status'>{products[currentProduct].availability}</span>
                  <span className='rating-display'>★ {products[currentProduct].rating}</span>
                </div>
              </div>

              {/* Product Description */}
              <div className='description-block'>
                <h3 className='section-title'>About This Watch</h3>
                <p className='description-text'>{products[currentProduct].description}</p>
              </div>

              {/* Key Features Grid */}
              <div className='features-grid'>
                <h3 className='section-title'>Premium Features</h3>
                <div className='features-list'>
                  {products[currentProduct].features.map((feature, index) => (
                    <div key={index} className='feature-item'>
                      <span className='feature-icon'>✓</span>
                      <span className='feature-text'>{feature}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Technical Specifications */}
              <div className='specs-section'>
                <h3 className='section-title'>Technical Specifications</h3>
                <div className='specs-grid'>
                  <div className='spec-row'>
                    <span className='spec-label'>Case Diameter</span>
                    <span className='spec-value'>{products[currentProduct].specifications.caseSize}</span>
                  </div>
                  <div className='spec-row'>
                    <span className='spec-label'>Case Thickness</span>
                    <span className='spec-value'>{products[currentProduct].specifications.thickness}</span>
                  </div>
                  <div className='spec-row'>
                    <span className='spec-label'>Case Material</span>
                    <span className='spec-value'>{products[currentProduct].specifications.material}</span>
                  </div>
                  <div className='spec-row'>
                    <span className='spec-label'>Movement Type</span>
                    <span className='spec-value'>{products[currentProduct].specifications.movement}</span>
                  </div>
                  <div className='spec-row'>
                    <span className='spec-label'>Water Resistance</span>
                    <span className='spec-value'>{products[currentProduct].specifications.waterResistance}</span>
                  </div>
                  <div className='spec-row'>
                    <span className='spec-label'>Warranty Period</span>
                    <span className='spec-value'>{products[currentProduct].specifications.warranty}</span>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className='action-buttons'>
                <button className='btn-primary'>
                  <span>Add to Cart</span>
                  <span className='btn-icon'>🛒</span>
                </button>
                <button className='btn-secondary'>
                  <span>Save to Wishlist</span>
                  <span className='btn-icon'>♡</span>
                </button>
              </div>

            </div>
          </div>
        </div>
      </section>

    </>
  );
}

export default Middle;
