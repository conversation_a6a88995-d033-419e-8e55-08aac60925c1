import '../styles/middle.css'

import Image1 from '../assets/images/image-1.jpeg'
import Image2 from '../assets/images/image-2.jpeg'
import Image3 from '../assets/images/image-3.jpeg'
import Image4 from '../assets/images/image-4.jpeg'
import Video1 from '../assets/video/video-1.mp4'
import Banner1 from '../assets/banner/banner-img1.png'

function Middle() {

  return (
    <>

      <div className="parent">
        <div className="div1">
          <img src={Image1} alt="Image 1" className="Image1" />
        </div>
        <div className="div2">
          <img src={Image2} alt="Image 2" className="Image2" />
        </div>
        <div className="div3">
          <video src={Video1} autoPlay loop muted className="Video1" />
        </div>
        <div className="div4">
          <img src={Image3} alt="Image 3" className="Image3" />
        </div>
        <div className="div5">
          <img src={Image4} alt="Image 4" className="Image4" />
        </div>
      </div>

      <div className="marquee-tilted-wrapper">
        <div className="marquee-wrapper">
          <div className="marquee-content">
            <span> Exclusive Offers & Discounts </span>
            <span> Limited Time Deals </span>
            <span> Save Big Today</span>
            <span> Shop Now & Save </span>
            <span> Special Festive Discounts </span>
          </div>
        </div>
      </div>

      <div className='banner'>
        <img src={Banner1} alt="Banner 1" id="Banner1" />
        <h1 id='banner-heading'>Shop Latest Models</h1>
        <p id='banner-subtittle'>Best Watches In The Segment</p>
        <button id="banner-button" className="button">
          <span id='button-text'>Explore</span>
        </button>

      </div>

      <div className='trending'>
        <div className='headings'>
          <h1 id='main-heading'>TRENDING Now</h1>
          <p id='subtittle'>Shop Now The Best Watches In The Market</p>
        </div>
        <div className="trending-images">
          <div className="trend1">
            <img src={Image1} alt="Image 1" className="Image1" />
          </div>
          <div className="trend2">
            <img src={Image2} alt="Image 2" className="Image2" />
          </div>
          <div className="trend3">
            <video src={Video1} autoPlay loop muted className="Video1" />
          </div>
          <div className="trend4">
            <img src={Image3} alt="Image 3" className="Image3" />
          </div>
          <div className="trend5">
            <img src={Image4} alt="Image 4" className="Image4" />
          </div>
        </div>
      </div>


      <div className='products'>
        <h1 id='product-heading'>PRODUCTS</h1>
        <p id='product-subtittle'>Shop Now The Best Watches In The Market</p>

        <div className='products-carousel'>
          <div className='product1'>
            <div className='product-image-container'>
              <img src={Image1} alt="Image 1" className="Image1" />
              <div className='product-controls'>
                <button className='control-btn active'>1</button>
                <button className='control-btn'>2</button>
                <button className='control-btn'>3</button>
                <button className='control-btn'>4</button>
              </div>
            </div>
            <div className='product-info'>
              <table>
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Price</th>
                    <th>Description</th>
                    <th>Mfg Date</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>Porsche-G16</td>
                    <td>$2500</td>
                    <td>Lorem ipsum dolor sit amet consectetur, adipisicing elit. Repellendus iure a earum officia dolore porro vero beatae, ducimus aut numquam suscipit officiis provident consequuntur iste soluta sapiente sequi repellat sunt?</td>
                    <td>12/02/2025</td>
                  </tr>
                </tbody>
              </table>

              <button id='product-button' className="button">
                <span id='button-text'>Add to Cart</span>
              </button>

            </div>
          </div>
          <div className='product2'>
            <div className='product-image-container'>
              <img src={Image2} alt="Image 2" className="Image2" />
              <div className='product-controls'>
                <button className='control-btn'>1</button>
                <button className='control-btn active'>2</button>
                <button className='control-btn'>3</button>
                <button className='control-btn'>4</button>
              </div>
            </div>
            <div className='product-info'>
              <table>
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Price</th>
                    <th>Description</th>
                    <th>Mfg Date</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>Porsche-T26</td>
                    <td>$3500</td>
                    <td>Lorem ipsum dolor sit amet consectetur, adipisicing elit. Repellendus iure a earum officia dolore porro vero beatae, ducimus aut numquam suscipit officiis provident consequuntur iste soluta sapiente sequi repellat sunt?</td>
                    <td>12/02/2024</td>
                  </tr>
                </tbody>
              </table>

              <button id='product-button' className="button">
                <span id='button-text'>Add to Cart</span>
              </button>

            </div>
          </div>
          <div className='product3'>
            <div className='product-image-container'>
              <img src={Image3} alt="Image 3" className="Image3" />
              <div className='product-controls'>
                <button className='control-btn'>1</button>
                <button className='control-btn'>2</button>
                <button className='control-btn active'>3</button>
                <button className='control-btn'>4</button>
              </div>
            </div>
            <div className='product-info'>
              <table>
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Price</th>
                    <th>Description</th>
                    <th>Mfg Date</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>Porsche-G12</td>
                    <td>$2800</td>
                    <td>Lorem ipsum dolor sit amet consectetur, adipisicing elit. Repellendus iure a earum officia dolore porro vero beatae, ducimus aut numquam suscipit officiis provident consequuntur iste soluta sapiente sequi repellat sunt?</td>
                    <td>12/01/2024</td>
                  </tr>
                </tbody>
              </table>

              <button id='product-button' className="button">
                <span id='button-text'>Add to Cart</span>
              </button>

            </div>
          </div>
          <div className='product4'>
            <div className='product-image-container'>
              <img src={Image4} alt="Image 4" className="Image4" />
              <div className='product-controls'>
                <button className='control-btn'>1</button>
                <button className='control-btn'>2</button>
                <button className='control-btn'>3</button>
                <button className='control-btn active'>4</button>
              </div>
            </div>
            <div className='product-info'>
              <table>
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Price</th>
                    <th>Description</th>
                    <th>Mfg Date</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>Porsche-U16</td>
                    <td>$2900</td>
                    <td>Lorem ipsum dolor sit amet consectetur, adipisicing elit. Repellendus iure a earum officia dolore porro vero beatae, ducimus aut numquam suscipit officiis provident consequuntur iste soluta sapiente sequi repellat sunt?</td>
                    <td>22/08/2024</td>
                  </tr>
                </tbody>
              </table>

              <button id='product-button' className="button">
                <span id='button-text'>Add to Cart</span>
              </button>

            </div>
          </div>
          <div className='product5'>
            <div className='product-image-container'>
              <img src={Image1} alt="Image 1" className="Image5" />
              <div className='product-controls'>
                <button className='control-btn'>1</button>
                <button className='control-btn'>2</button>
                <button className='control-btn'>3</button>
                <button className='control-btn'>4</button>
                <button className='control-btn active'>5</button>
              </div>
            </div>
            <div className='product-info'>
              <table>
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Price</th>
                    <th>Description</th>
                    <th>Mfg Date</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>Porsche-R-9</td>
                    <td>$4500</td>
                    <td>Lorem ipsum dolor sit amet consectetur, adipisicing elit. Repellendus iure a earum officia dolore porro vero beatae, ducimus aut numquam suscipit officiis provident consequuntur iste soluta sapiente sequi repellat sunt?</td>
                    <td>28/12/2023</td>
                  </tr>
                </tbody>
              </table>

              <button id='product-button' className="button">
                <span id='button-text'>Add to Cart</span>
              </button>

            </div>
          </div>
        </div>

      </div>

    </>
  );
}

export default Middle;
