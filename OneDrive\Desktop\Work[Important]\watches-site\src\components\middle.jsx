import '../styles/middle.css'
import { useState } from 'react'

import Image1 from '../assets/images/image-1.jpeg'
import Image2 from '../assets/images/image-2.jpeg'
import Image3 from '../assets/images/image-3.jpeg'
import Image4 from '../assets/images/image-4.jpeg'
import Video1 from '../assets/video/video-1.mp4'
import Banner1 from '../assets/banner/banner-img1.png'

function Middle() {
  const [currentProduct, setCurrentProduct] = useState(0);

  const products = [
    {
      id: 1,
      image: Image1,
      name: "Porsche Design Chronotimer Series 1",
      model: "PD-G16-2025",
      price: "$2,500",
      originalPrice: "$3,200",
      discount: "22% OFF",
      description: "Premium luxury timepiece featuring Swiss automatic movement with 42-hour power reserve. Crafted with titanium case and sapphire crystal glass for ultimate durability.",
      features: [
        "Swiss Automatic Movement",
        "Titanium Case Construction",
        "Sapphire Crystal Glass",
        "Water Resistant 300m",
        "Anti-Magnetic Protection",
        "Luminous Hands & Markers"
      ],
      specifications: {
        caseSize: "42mm",
        thickness: "12mm",
        material: "Grade 5 Titanium",
        movement: "Swiss ETA 2824-2",
        waterResistance: "300m/1000ft",
        warranty: "5 Years International"
      },
      mfgDate: "12/02/2025",
      availability: "In Stock",
      rating: "4.9/5",
      bgClass: "product1"
    },
    {
      id: 2,
      image: Image2,
      name: "Porsche Design Timemaster Elite",
      model: "PD-T26-2024",
      price: "$3,500",
      originalPrice: "$4,200",
      discount: "17% OFF",
      description: "Sophisticated chronograph with carbon fiber bezel and ceramic case back. Features advanced GMT function and perpetual calendar for the modern professional.",
      features: [
        "Chronograph Function",
        "GMT Dual Time Zone",
        "Carbon Fiber Bezel",
        "Ceramic Case Back",
        "Perpetual Calendar",
        "Tachymeter Scale"
      ],
      specifications: {
        caseSize: "44mm",
        thickness: "14mm",
        material: "Stainless Steel & Carbon",
        movement: "Swiss Valjoux 7750",
        waterResistance: "200m/660ft",
        warranty: "3 Years International"
      },
      mfgDate: "12/02/2024",
      availability: "Limited Edition",
      rating: "4.8/5",
      bgClass: "product2"
    },
    {
      id: 3,
      image: Image3,
      name: "Porsche Design Racing Collection",
      model: "PD-G12-2024",
      price: "$2,800",
      originalPrice: "$3,400",
      discount: "18% OFF",
      description: "Inspired by motorsport heritage, this timepiece features racing-inspired design elements with high-performance materials and precision engineering.",
      features: [
        "Racing Inspired Design",
        "Lightweight Aluminum",
        "Quick Release Strap",
        "Shock Resistant",
        "Date Display",
        "Luminous Coating"
      ],
      specifications: {
        caseSize: "40mm",
        thickness: "11mm",
        material: "Aluminum Alloy",
        movement: "Swiss Quartz",
        waterResistance: "100m/330ft",
        warranty: "2 Years International"
      },
      mfgDate: "12/01/2024",
      availability: "In Stock",
      rating: "4.7/5",
      bgClass: "product3"
    },
    {
      id: 4,
      image: Image4,
      name: "Porsche Design Urban Explorer",
      model: "PD-U16-2024",
      price: "$2,900",
      originalPrice: "$3,600",
      discount: "19% OFF",
      description: "Urban lifestyle watch with smart connectivity features. Combines traditional craftsmanship with modern technology for the contemporary explorer.",
      features: [
        "Smart Connectivity",
        "GPS Navigation",
        "Heart Rate Monitor",
        "Wireless Charging",
        "Multiple Time Zones",
        "Activity Tracking"
      ],
      specifications: {
        caseSize: "43mm",
        thickness: "13mm",
        material: "Stainless Steel",
        movement: "Hybrid Smart Movement",
        waterResistance: "150m/500ft",
        warranty: "2 Years + 1 Year Tech"
      },
      mfgDate: "22/08/2024",
      availability: "Pre-Order",
      rating: "4.6/5",
      bgClass: "product4"
    },
    {
      id: 5,
      image: Image1,
      name: "Porsche Design Heritage Classic",
      model: "PD-R9-2023",
      price: "$4,500",
      originalPrice: "$5,800",
      discount: "22% OFF",
      description: "Vintage-inspired masterpiece celebrating Porsche's rich heritage. Hand-assembled with premium materials and traditional watchmaking techniques.",
      features: [
        "Hand-Assembled Movement",
        "18K Gold Accents",
        "Leather Strap Premium",
        "Limited Production",
        "Vintage Inspired",
        "Collector's Edition"
      ],
      specifications: {
        caseSize: "41mm",
        thickness: "12.5mm",
        material: "18K Gold & Steel",
        movement: "Swiss Manual Wind",
        waterResistance: "50m/165ft",
        warranty: "Lifetime Service"
      },
      mfgDate: "28/12/2023",
      availability: "Collector's Item",
      rating: "5.0/5",
      bgClass: "product5"
    }
  ];

  const handleProductChange = (index) => {
    setCurrentProduct(index);
  };

  return (
    <>

      <div className="parent">
        <div className="div1">
          <img src={Image1} alt="Image 1" className="Image1" />
        </div>
        <div className="div2">
          <img src={Image2} alt="Image 2" className="Image2" />
        </div>
        <div className="div3">
          <video src={Video1} autoPlay loop muted className="Video1" />
        </div>
        <div className="div4">
          <img src={Image3} alt="Image 3" className="Image3" />
        </div>
        <div className="div5">
          <img src={Image4} alt="Image 4" className="Image4" />
        </div>
      </div>

      <div className="marquee-tilted-wrapper">
        <div className="marquee-wrapper">
          <div className="marquee-content">
            <span> Exclusive Offers & Discounts </span>
            <span> Limited Time Deals </span>
            <span> Save Big Today</span>
            <span> Shop Now & Save </span>
            <span> Special Festive Discounts </span>
          </div>
        </div>
      </div>

      <div className='banner'>
        <img src={Banner1} alt="Banner 1" id="Banner1" />
        <h1 id='banner-heading'>Shop Latest Models</h1>
        <p id='banner-subtittle'>Best Watches In The Segment</p>
        <button id="banner-button" className="button">
          <span id='button-text'>Explore</span>
        </button>

      </div>

      <div className='trending'>
        <div className='headings'>
          <h1 id='main-heading'>TRENDING Now</h1>
          <p id='subtittle'>Shop Now The Best Watches In The Market</p>
        </div>
        <div className="trending-images">
          <div className="trend1">
            <img src={Image1} alt="Image 1" className="Image1" />
          </div>
          <div className="trend2">
            <img src={Image2} alt="Image 2" className="Image2" />
          </div>
          <div className="trend3">
            <video src={Video1} autoPlay loop muted className="Video1" />
          </div>
          <div className="trend4">
            <img src={Image3} alt="Image 3" className="Image3" />
          </div>
          <div className="trend5">
            <img src={Image4} alt="Image 4" className="Image4" />
          </div>
        </div>
      </div>


      <div className='products'>
        <h1 id='product-heading'>PRODUCTS</h1>
        <p id='product-subtittle'>Shop Now The Best Watches In The Market</p>

        <div className='products-carousel'>
          <div className={`single-product ${products[currentProduct].bgClass}`}>
            <div className='product-image-container'>
              <img src={products[currentProduct].image} alt={products[currentProduct].name} />
              <div className='product-controls'>
                {products.map((_, index) => (
                  <button
                    key={index}
                    className={`control-btn ${currentProduct === index ? 'active' : ''}`}
                    onClick={() => handleProductChange(index)}
                  >
                    {index + 1}
                  </button>
                ))}
              </div>
            </div>
            <div className='product-info'>
              <div className='product-header'>
                <h2 className='product-name'>{products[currentProduct].name}</h2>
                <p className='product-model'>Model: {products[currentProduct].model}</p>
                <div className='product-pricing'>
                  <span className='current-price'>{products[currentProduct].price}</span>
                  <span className='original-price'>{products[currentProduct].originalPrice}</span>
                  <span className='discount'>{products[currentProduct].discount}</span>
                </div>
                <div className='product-meta'>
                  <span className='availability'>{products[currentProduct].availability}</span>
                  <span className='rating'>★ {products[currentProduct].rating}</span>
                </div>
              </div>

              <div className='product-description'>
                <h3>Description</h3>
                <p>{products[currentProduct].description}</p>
              </div>

              <div className='product-features'>
                <h3>Key Features</h3>
                <ul>
                  {products[currentProduct].features.map((feature, index) => (
                    <li key={index}>{feature}</li>
                  ))}
                </ul>
              </div>

              <div className='product-specifications'>
                <h3>Specifications</h3>
                <table className='specs-table'>
                  <tbody>
                    <tr>
                      <td>Case Size</td>
                      <td>{products[currentProduct].specifications.caseSize}</td>
                    </tr>
                    <tr>
                      <td>Thickness</td>
                      <td>{products[currentProduct].specifications.thickness}</td>
                    </tr>
                    <tr>
                      <td>Material</td>
                      <td>{products[currentProduct].specifications.material}</td>
                    </tr>
                    <tr>
                      <td>Movement</td>
                      <td>{products[currentProduct].specifications.movement}</td>
                    </tr>
                    <tr>
                      <td>Water Resistance</td>
                      <td>{products[currentProduct].specifications.waterResistance}</td>
                    </tr>
                    <tr>
                      <td>Warranty</td>
                      <td>{products[currentProduct].specifications.warranty}</td>
                    </tr>
                    <tr>
                      <td>Manufacturing Date</td>
                      <td>{products[currentProduct].mfgDate}</td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <div className='product-actions'>
                <button id='product-button' className="add-to-cart-btn">
                  <span>Add to Cart</span>
                </button>
                <button className="wishlist-btn">
                  <span>♡ Add to Wishlist</span>
                </button>
              </div>

            </div>
          </div>
        </div>

      </div>

    </>
  );
}

export default Middle;
