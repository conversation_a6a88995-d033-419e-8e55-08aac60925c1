import '../styles/middle.css'
import { useState } from 'react'

import Image1 from '../assets/images/image-1.jpeg'
import Image2 from '../assets/images/image-2.jpeg'
import Image3 from '../assets/images/image-3.jpeg'
import Image4 from '../assets/images/image-4.jpeg'
import Video1 from '../assets/video/video-1.mp4'
import Banner1 from '../assets/banner/banner-img1.png'

function Middle() {
  const [currentProduct, setCurrentProduct] = useState(0);

  const products = [
    {
      id: 1,
      image: Image1,
      name: "Porsche-G16",
      price: "$2500",
      description: "Lorem ipsum dolor sit amet consectetur, adipisicing elit. Repellendus iure a earum officia dolore porro vero beatae, ducimus aut numquam suscipit officiis provident consequuntur iste soluta sapiente sequi repellat sunt?",
      mfgDate: "12/02/2025",
      bgClass: "product1"
    },
    {
      id: 2,
      image: Image2,
      name: "Porsche-T26",
      price: "$3500",
      description: "Lorem ipsum dolor sit amet consectetur, adipisicing elit. Repellendus iure a earum officia dolore porro vero beatae, ducimus aut numquam suscipit officiis provident consequuntur iste soluta sapiente sequi repellat sunt?",
      mfgDate: "12/02/2024",
      bgClass: "product2"
    },
    {
      id: 3,
      image: Image3,
      name: "Porsche-G12",
      price: "$2800",
      description: "Lorem ipsum dolor sit amet consectetur, adipisicing elit. Repellendus iure a earum officia dolore porro vero beatae, ducimus aut numquam suscipit officiis provident consequuntur iste soluta sapiente sequi repellat sunt?",
      mfgDate: "12/01/2024",
      bgClass: "product3"
    },
    {
      id: 4,
      image: Image4,
      name: "Porsche-U16",
      price: "$2900",
      description: "Lorem ipsum dolor sit amet consectetur, adipisicing elit. Repellendus iure a earum officia dolore porro vero beatae, ducimus aut numquam suscipit officiis provident consequuntur iste soluta sapiente sequi repellat sunt?",
      mfgDate: "22/08/2024",
      bgClass: "product4"
    },
    {
      id: 5,
      image: Image1,
      name: "Porsche-R-9",
      price: "$4500",
      description: "Lorem ipsum dolor sit amet consectetur, adipisicing elit. Repellendus iure a earum officia dolore porro vero beatae, ducimus aut numquam suscipit officiis provident consequuntur iste soluta sapiente sequi repellat sunt?",
      mfgDate: "28/12/2023",
      bgClass: "product5"
    }
  ];

  const handleProductChange = (index) => {
    setCurrentProduct(index);
  };

  return (
    <>

      <div className="parent">
        <div className="div1">
          <img src={Image1} alt="Image 1" className="Image1" />
        </div>
        <div className="div2">
          <img src={Image2} alt="Image 2" className="Image2" />
        </div>
        <div className="div3">
          <video src={Video1} autoPlay loop muted className="Video1" />
        </div>
        <div className="div4">
          <img src={Image3} alt="Image 3" className="Image3" />
        </div>
        <div className="div5">
          <img src={Image4} alt="Image 4" className="Image4" />
        </div>
      </div>

      <div className="marquee-tilted-wrapper">
        <div className="marquee-wrapper">
          <div className="marquee-content">
            <span> Exclusive Offers & Discounts </span>
            <span> Limited Time Deals </span>
            <span> Save Big Today</span>
            <span> Shop Now & Save </span>
            <span> Special Festive Discounts </span>
          </div>
        </div>
      </div>

      <div className='banner'>
        <img src={Banner1} alt="Banner 1" id="Banner1" />
        <h1 id='banner-heading'>Shop Latest Models</h1>
        <p id='banner-subtittle'>Best Watches In The Segment</p>
        <button id="banner-button" className="button">
          <span id='button-text'>Explore</span>
        </button>

      </div>

      <div className='trending'>
        <div className='headings'>
          <h1 id='main-heading'>TRENDING Now</h1>
          <p id='subtittle'>Shop Now The Best Watches In The Market</p>
        </div>
        <div className="trending-images">
          <div className="trend1">
            <img src={Image1} alt="Image 1" className="Image1" />
          </div>
          <div className="trend2">
            <img src={Image2} alt="Image 2" className="Image2" />
          </div>
          <div className="trend3">
            <video src={Video1} autoPlay loop muted className="Video1" />
          </div>
          <div className="trend4">
            <img src={Image3} alt="Image 3" className="Image3" />
          </div>
          <div className="trend5">
            <img src={Image4} alt="Image 4" className="Image4" />
          </div>
        </div>
      </div>


      <div className='products'>
        <h1 id='product-heading'>PRODUCTS</h1>
        <p id='product-subtittle'>Shop Now The Best Watches In The Market</p>

        <div className='products-carousel'>
          <div className={`single-product ${products[currentProduct].bgClass}`}>
            <div className='product-image-container'>
              <img src={products[currentProduct].image} alt={products[currentProduct].name} />
              <div className='product-controls'>
                {products.map((_, index) => (
                  <button
                    key={index}
                    className={`control-btn ${currentProduct === index ? 'active' : ''}`}
                    onClick={() => handleProductChange(index)}
                  >
                    {index + 1}
                  </button>
                ))}
              </div>
            </div>
            <div className='product-info'>
              <table>
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Price</th>
                    <th>Description</th>
                    <th>Mfg Date</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>{products[currentProduct].name}</td>
                    <td>{products[currentProduct].price}</td>
                    <td>{products[currentProduct].description}</td>
                    <td>{products[currentProduct].mfgDate}</td>
                  </tr>
                </tbody>
              </table>

              <button id='product-button' className="button">
                <span id='button-text'>Add to Cart</span>
              </button>

            </div>
          </div>
        </div>

      </div>

    </>
  );
}

export default Middle;
