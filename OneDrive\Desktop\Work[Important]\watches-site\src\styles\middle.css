@font-face {
  font-family: 'Boldivia';
  src: url('../src/assets/fonts/boldovia.regular.ttf') format('truetype');
  font-style: normal;
  font-weight: normal;
}

.middle-background {
  position: relative;
  width: 100%;
  height: 100%;
  left: 0;
  z-index: 0;
  background: rgb(0, 0, 0);
  min-height: 100vh;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
}


.parent {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-auto-rows: minmax(200px, auto);
  gap: 20px;
  padding: 20px;
  background: rgb(0, 0, 0);
  max-height: 500px;
}

/* Grid Item Sizes to Match Screenshot */
.div1 {
  grid-row: span 2;
  cursor: pointer;
}

.div2 {
  grid-column: 2;
  grid-row: 1;
  cursor: pointer;
}

.div3 {
  grid-column: 2;
  grid-row: 2;
  cursor: pointer;
}

.div4 {
  grid-column: 3;
  grid-row: 1;
  cursor: pointer;
}

.div5 {
  grid-column: 3;
  grid-row: 2;
  cursor: pointer;
}

.div1:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.div2:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.div3:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.div4:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.div5:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

/* Optional: Media Styling */
img,
video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
}

/*===========================Marquee start=================================================================*/
.marquee-tilted-wrapper {
  overflow: hidden;
  background: rgb(0, 0, 0);
}

.marquee-wrapper {
  overflow: hidden;
  width: 100%;
}

.marquee-content {
  display: flex;
  gap: 4rem;
  min-width: 100%;
  white-space: nowrap;
  animation: scrollMarquee 15s linear infinite;
}

.marquee-content span {
  font-size: 2rem;
  font-weight: bold;
  color: #d35400;
}

/* Keyframes for infinite scroll */
@keyframes scrollMarquee {
  0% {
    transform: translateX(100%);
  }

  100% {
    transform: translateX(-150%);
  }
}

/*===========================Marquee end=================================================================*/

.banner {
  display: flex;
  position: relative;
  justify-content: center;
  align-items: center;
  border-radius: 0;
  width: 100%;
  object-fit: fill;
  object-position: center;
}

#Banner1 {
  width: 100%;
  height: 100%;
  border-radius: 0%;
  object-fit: fill;
  object-position: center;
}


#Banner1 {
  width: 100%;
  height: 600px;
  display: block;
}

#banner-heading {
  position: absolute;
  font-size: clamp(2.5rem, 6vw, 4.5rem);
  top: 15%;
  left: 25%;
  transform: translate(-50%, -50%);
  font-family: 'boldivia';
  color: rgb(255, 255, 255);
  font-weight: bold;

  pointer-events: none;
}

#banner-subtittle {
  position: absolute;
  font-size: clamp(1.125rem, 3vw, 1.75rem);
  top: 32%;
  left: 25%;
  transform: translate(-50%, -50%);
  font-family: 'boldivia';
  color: rgb(255, 255, 255);
  font-weight: bold;

  pointer-events: none;
}

#banner-button {
  position: absolute;
  top: 50%;
  left: 20%;

  font-size: 1.25rem;
  font-weight: bold;
  cursor: pointer;
}

/* From Uiverse.io by mrhyddenn */ 
button {
  background: #fff;
  border: none;
  padding: 10px 10px;
  display: inline-block;
  font-size: 15px;
  font-weight: 600;
  width: 120px;
  text-transform: uppercase;
  cursor: pointer;
  transform: skew(-21deg);
}

#button-text {
  display: inline-block;
  transform: skew(21deg);
}

button::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  right: 100%;
  left: 0;
  background: rgb(20, 20, 20);
  opacity: 0;
  z-index: -1;
  transition: all 0.5s;
}

button:hover {
  color: #fff;
}

button:hover::before {
  left: 0;
  right: 0;
  opacity: 1;
}




/*====================Trending Now start=====================================================*/

.headings {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-family: "boldivia";
  background: rgb(0, 0, 0);
}

#main-heading {
  color: #ffffff;
  font-size: 5rem;
  margin-top: 0rem;
}

#subtittle {
  color: rgb(255, 255, 255);
  font-size: 1rem;
  margin-top: -4rem;
}


.trending-images {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-template-rows: repeat(4, 1fr);
  gap: 20px;
  padding: 20px;
  background: rgb(0, 0, 0);
  max-height: 380px;
}

.trend1 {
  grid-row: span 4 / span 4;
  height: 350px;
  width: 280px;
  cursor: pointer;
}

.trend2 {
  grid-row: span 4 / span 4;
  height: 350px;
  width: 280px;
  cursor: pointer;
}

.trend3 {
  grid-row: span 4 / span 4;
  height: 350px;
  width: 280px;
  cursor: pointer;
}

.trend4 {
  grid-row: span 4 / span 4;
  height: 350px;
  width: 280px;
  cursor: pointer;
}

.trend5 {
  grid-row: span 4 / span 4;
  height: 350px;
  width: 280px;
  cursor: pointer;
}

.trend1:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.trend2:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.trend3:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.trend4:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.trend5:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}



/*====================Trending Now end=====================================================*/


/*====================Products start=====================================================*/

.products {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-family: "boldivia";
  color: white;
  background: rgb(0, 0, 0);
  padding: 40px 20px;
}

#product-heading {
  font-size: 4rem;
  margin-bottom: 10px;
  color: #ffffff;
  text-align: center;
}

#product-subtittle {
  font-size: 1.2rem;
  margin-bottom: 40px;
  color: #cccccc;
  text-align: center;
}

.products-carousel {
  display: flex;
  justify-content: center;
  width: 100%;
  max-width: 1200px;
}

.single-product {
  display: flex;
  align-items: center;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  transition: all 0.5s ease;
  width: 100%;
  opacity: 0;
  animation: fadeIn 0.5s ease forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.single-product:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.5);
}

/* Product Background Classes */
.single-product.product1 {
  background: linear-gradient(135deg, #2c1810, #4a2c5a);
  box-shadow: 0 8px 25px rgba(76, 44, 90, 0.3);
}

.single-product.product1:hover {
  box-shadow: 0 12px 35px rgba(76, 44, 90, 0.5);
}

.single-product.product2 {
  background: linear-gradient(135deg, #1a2332, #2c4a6b);
  box-shadow: 0 8px 25px rgba(44, 74, 107, 0.3);
}

.single-product.product2:hover {
  box-shadow: 0 12px 35px rgba(44, 74, 107, 0.5);
}

.single-product.product3 {
  background: linear-gradient(135deg, #1a2e1a, #2d5a2d);
  box-shadow: 0 8px 25px rgba(45, 90, 45, 0.3);
}

.single-product.product3:hover {
  box-shadow: 0 12px 35px rgba(45, 90, 45, 0.5);
}

.single-product.product4 {
  background: linear-gradient(135deg, #2e1a1a, #5a2d2d);
  box-shadow: 0 8px 25px rgba(90, 45, 45, 0.3);
}

.single-product.product4:hover {
  box-shadow: 0 12px 35px rgba(90, 45, 45, 0.5);
}

.single-product.product5 {
  background: linear-gradient(135deg, #2e1f1a, #5a3d2d);
  box-shadow: 0 8px 25px rgba(90, 61, 45, 0.3);
}

.single-product.product5:hover {
  box-shadow: 0 12px 35px rgba(90, 61, 45, 0.5);
}

/* Image Container */
.product-image-container {
  flex: 0 0 300px;
  margin-right: 30px;
  position: relative;
}

.product-image-container img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

/* Control Buttons */
.product-controls {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 15px;
}

.control-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all 0.3s ease;
  transform: none;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.1);
}

.control-btn.active {
  background: rgba(255, 255, 255, 0.4);
  border-color: rgba(255, 255, 255, 0.7);
}

/* Product Info */
.product-info {
  flex: 1;
  color: white;
}

.product-info table {
  width: 100%;
  border-collapse: collapse;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 20px;
}

.product-info th {
  background: rgba(255, 255, 255, 0.1);
  padding: 15px;
  text-align: left;
  font-weight: bold;
  color: #ffffff;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.product-info td {
  padding: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  color: #e0e0e0;
}

.product-info td:first-child {
  font-weight: bold;
  color: #ffffff;
}

.product-info td:nth-child(2) {
  font-weight: bold;
  color: #4CAF50;
  font-size: 1.1em;
}

#product-button {
  background: rgba(255, 255, 255, 0.9);
  color: #000;
  border: none;
  padding: 12px 25px;
  font-size: 16px;
  font-weight: bold;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  transform: none;
  width: auto;
  min-width: 150px;
}

#product-button:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

#product-button #button-text {
  transform: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .single-product {
    flex-direction: column;
    text-align: center;
    padding: 20px;
  }

  .product-image-container {
    flex: none;
    margin-right: 0;
    margin-bottom: 20px;
    width: 100%;
  }

  .product-info table {
    font-size: 14px;
  }

  .product-info th,
  .product-info td {
    padding: 10px;
  }

  .control-btn {
    width: 35px;
    height: 35px;
    font-size: 14px;
  }
}

/*====================Products end=====================================================*/