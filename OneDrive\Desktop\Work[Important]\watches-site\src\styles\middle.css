@font-face {
  font-family: 'Boldivia';
  src: url('../src/assets/fonts/boldovia.regular.ttf') format('truetype');
  font-style: normal;
  font-weight: normal;
}

.middle-background {
  position: relative;
  width: 100%;
  height: 100%;
  left: 0;
  z-index: 0;
  background: rgb(0, 0, 0);
  min-height: 100vh;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
}


.parent {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-auto-rows: minmax(200px, auto);
  gap: 20px;
  padding: 20px;
  background: rgb(0, 0, 0);
  max-height: 500px;
}

/* Grid Item Sizes to Match Screenshot */
.div1 {
  grid-row: span 2;
  cursor: pointer;
}

.div2 {
  grid-column: 2;
  grid-row: 1;
  cursor: pointer;
}

.div3 {
  grid-column: 2;
  grid-row: 2;
  cursor: pointer;
}

.div4 {
  grid-column: 3;
  grid-row: 1;
  cursor: pointer;
}

.div5 {
  grid-column: 3;
  grid-row: 2;
  cursor: pointer;
}

.div1:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.div2:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.div3:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.div4:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.div5:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

/* Optional: Media Styling */
img,
video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
}

/*===========================Marquee start=================================================================*/
.marquee-tilted-wrapper {
  overflow: hidden;
  background: rgb(0, 0, 0);
}

.marquee-wrapper {
  overflow: hidden;
  width: 100%;
}

.marquee-content {
  display: flex;
  gap: 4rem;
  min-width: 100%;
  white-space: nowrap;
  animation: scrollMarquee 15s linear infinite;
}

.marquee-content span {
  font-size: 2rem;
  font-weight: bold;
  color: #d35400;
}

/* Keyframes for infinite scroll */
@keyframes scrollMarquee {
  0% {
    transform: translateX(100%);
  }

  100% {
    transform: translateX(-150%);
  }
}

/*===========================Marquee end=================================================================*/

.banner {
  display: flex;
  position: relative;
  justify-content: center;
  align-items: center;
  border-radius: 0;
  width: 100%;
  object-fit: fill;
  object-position: center;
}

#Banner1 {
  width: 100%;
  height: 100%;
  border-radius: 0%;
  object-fit: fill;
  object-position: center;
}


#Banner1 {
  width: 100%;
  height: 600px;
  display: block;
}

#banner-heading {
  position: absolute;
  font-size: clamp(2.5rem, 6vw, 4.5rem);
  top: 15%;
  left: 25%;
  transform: translate(-50%, -50%);
  font-family: 'boldivia';
  color: rgb(255, 255, 255);
  font-weight: bold;

  pointer-events: none;
}

#banner-subtittle {
  position: absolute;
  font-size: clamp(1.125rem, 3vw, 1.75rem);
  top: 32%;
  left: 25%;
  transform: translate(-50%, -50%);
  font-family: 'boldivia';
  color: rgb(255, 255, 255);
  font-weight: bold;

  pointer-events: none;
}

#banner-button {
  position: absolute;
  top: 50%;
  left: 20%;

  font-size: 1.25rem;
  font-weight: bold;
  cursor: pointer;
}

/* From Uiverse.io by mrhyddenn */ 
button {
  background: #fff;
  border: none;
  padding: 10px 10px;
  display: inline-block;
  font-size: 15px;
  font-weight: 600;
  width: 120px;
  text-transform: uppercase;
  cursor: pointer;
  transform: skew(-21deg);
}

#button-text {
  display: inline-block;
  transform: skew(21deg);
}

button::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  right: 100%;
  left: 0;
  background: rgb(20, 20, 20);
  opacity: 0;
  z-index: -1;
  transition: all 0.5s;
}

button:hover {
  color: #fff;
}

button:hover::before {
  left: 0;
  right: 0;
  opacity: 1;
}




/*====================Trending Now start=====================================================*/

.headings {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-family: "boldivia";
  background: rgb(0, 0, 0);
}

#main-heading {
  color: #ffffff;
  font-size: 5rem;
  margin-top: 0rem;
}

#subtittle {
  color: rgb(255, 255, 255);
  font-size: 1rem;
  margin-top: -4rem;
}


.trending-images {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-template-rows: repeat(4, 1fr);
  gap: 20px;
  padding: 20px;
  background: rgb(0, 0, 0);
  max-height: 380px;
}

.trend1 {
  grid-row: span 4 / span 4;
  height: 350px;
  width: 280px;
  cursor: pointer;
}

.trend2 {
  grid-row: span 4 / span 4;
  height: 350px;
  width: 280px;
  cursor: pointer;
}

.trend3 {
  grid-row: span 4 / span 4;
  height: 350px;
  width: 280px;
  cursor: pointer;
}

.trend4 {
  grid-row: span 4 / span 4;
  height: 350px;
  width: 280px;
  cursor: pointer;
}

.trend5 {
  grid-row: span 4 / span 4;
  height: 350px;
  width: 280px;
  cursor: pointer;
}

.trend1:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.trend2:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.trend3:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.trend4:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.trend5:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}



/*====================Trending Now end=====================================================*/


/*====================Products start=====================================================*/

.products {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  font-family: "boldivia";
  color: white;
  background: rgb(0, 0, 0);
  padding: 60px 0 0 0;
  min-height: 100vh;
}

#product-heading {
  font-size: 5rem;
  margin-top: 0rem;
  color: #ffffff;
  text-align: center;
  z-index: 10;
  position: relative;
}

#product-subtittle {
  font-size: 1.5rem;
  margin-top: 0rem;
  color: #cccccc;
  text-align: center;
  z-index: 10;
  position: relative;
}

.products-carousel {
  display: flex;
  justify-content: center;
  width: 100vw;
  height: calc(100vh - 200px);
  position: relative;
  margin-top: 0rem;
}

.single-product {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0;
  padding: 60px 80px;
  box-shadow: inset 0 0 100px rgba(0, 0, 0, 0.3);
  transition: all 0.5s ease;
  width: 100vw;
  height: 100%;
  opacity: 0;
  animation: fadeIn 0.5s ease forwards;
  position: relative;
  overflow: hidden;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.single-product:hover {
  box-shadow: inset 0 0 120px rgba(0, 0, 0, 0.4);
}

/* Product Background Classes */
.single-product.product1 {
  background: linear-gradient(135deg, #2c1810, #4a2c5a);
  box-shadow: 0 8px 25px rgba(76, 44, 90, 0.3);
}

.single-product.product1:hover {
  box-shadow: 0 12px 35px rgba(76, 44, 90, 0.5);
}

.single-product.product2 {
  background: linear-gradient(135deg, #1a2332, #2c4a6b);
  box-shadow: 0 8px 25px rgba(44, 74, 107, 0.3);
}

.single-product.product2:hover {
  box-shadow: 0 12px 35px rgba(44, 74, 107, 0.5);
}

.single-product.product3 {
  background: linear-gradient(135deg, #1a2e1a, #2d5a2d);
  box-shadow: 0 8px 25px rgba(45, 90, 45, 0.3);
}

.single-product.product3:hover {
  box-shadow: 0 12px 35px rgba(45, 90, 45, 0.5);
}

.single-product.product4 {
  background: linear-gradient(135deg, #2e1a1a, #5a2d2d);
  box-shadow: 0 8px 25px rgba(90, 45, 45, 0.3);
}

.single-product.product4:hover {
  box-shadow: 0 12px 35px rgba(90, 45, 45, 0.5);
}

.single-product.product5 {
  background: linear-gradient(135deg, #2e1f1a, #5a3d2d);
  box-shadow: 0 8px 25px rgba(90, 61, 45, 0.3);
}

.single-product.product5:hover {
  box-shadow: 0 12px 35px rgba(90, 61, 45, 0.5);
}

/* Image Container */
.product-image-container {
  flex: 0 0 500px;
  margin-right: 60px;
  position: relative;
}

.product-image-container img {
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  border: 3px solid rgba(255, 255, 255, 0.1);
}

/* Control Buttons */
.product-controls {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 25px;
}

.control-btn {
  background: rgba(255, 255, 255, 0.15);
  border: 3px solid rgba(255, 255, 255, 0.3);
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  transition: all 0.3s ease;
  transform: none;
  backdrop-filter: blur(10px);
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.6);
  transform: scale(1.15);
  box-shadow: 0 5px 15px rgba(255, 255, 255, 0.2);
}

.control-btn.active {
  background: rgba(255, 255, 255, 0.4);
  border-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 5px 20px rgba(255, 255, 255, 0.3);
}

/* Product Info */
.product-info {
  flex: 1;
  color: white;
  max-width: 600px;
  padding: 20px;
}

/* Product Header */
.product-header {
  margin-bottom: 30px;
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 20px;
}

.product-name {
  font-size: 2.5rem;
  font-weight: bold;
  margin: 0 0 10px 0;
  color: #ffffff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.product-model {
  font-size: 1.2rem;
  color: #cccccc;
  margin: 0 0 15px 0;
  font-style: italic;
}

.product-pricing {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.current-price {
  font-size: 2rem;
  font-weight: bold;
  color: #4CAF50;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.original-price {
  font-size: 1.3rem;
  color: #999;
  text-decoration: line-through;
}

.discount {
  background: #ff4444;
  color: white;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 0.9rem;
  font-weight: bold;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.availability {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  padding: 5px 15px;
  border-radius: 20px;
  font-size: 0.9rem;
  border: 1px solid #4CAF50;
}

.rating {
  color: #FFD700;
  font-size: 1.1rem;
  font-weight: bold;
}

/* Product Description */
.product-description {
  margin-bottom: 25px;
}

.product-description h3 {
  font-size: 1.4rem;
  color: #ffffff;
  margin-bottom: 10px;
  border-left: 4px solid #4CAF50;
  padding-left: 15px;
}

.product-description p {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #e0e0e0;
  margin: 0;
}

/* Product Features */
.product-features {
  margin-bottom: 25px;
}

.product-features h3 {
  font-size: 1.4rem;
  color: #ffffff;
  margin-bottom: 15px;
  border-left: 4px solid #2196F3;
  padding-left: 15px;
}

.product-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.product-features li {
  background: rgba(255, 255, 255, 0.05);
  padding: 10px 15px;
  border-radius: 8px;
  color: #e0e0e0;
  border-left: 3px solid #2196F3;
  transition: all 0.3s ease;
}

.product-features li:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(5px);
}

/* Product Specifications */
.product-specifications {
  margin-bottom: 30px;
}

.product-specifications h3 {
  font-size: 1.4rem;
  color: #ffffff;
  margin-bottom: 15px;
  border-left: 4px solid #FF9800;
  padding-left: 15px;
}

.specs-table {
  width: 100%;
  border-collapse: collapse;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.specs-table td {
  padding: 12px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.specs-table td:first-child {
  font-weight: bold;
  color: #ffffff;
  background: rgba(255, 255, 255, 0.05);
  width: 40%;
}

.specs-table td:last-child {
  color: #e0e0e0;
}

.specs-table tr:last-child td {
  border-bottom: none;
}

/* Product Actions */
.product-actions {
  display: flex;
  gap: 20px;
  margin-top: 30px;
}

.add-to-cart-btn {
  background: linear-gradient(45deg, #4CAF50, #45a049);
  color: white;
  border: none;
  padding: 15px 30px;
  font-size: 1.1rem;
  font-weight: bold;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  transform: none;
  flex: 1;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.add-to-cart-btn:hover {
  background: linear-gradient(45deg, #45a049, #4CAF50);
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.wishlist-btn {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 15px 25px;
  font-size: 1.1rem;
  font-weight: bold;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.wishlist-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-3px);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .single-product {
    padding: 40px 50px;
  }

  .product-image-container {
    flex: 0 0 400px;
    margin-right: 40px;
  }

  .product-image-container img {
    height: 320px;
  }

  .product-name {
    font-size: 2rem;
  }

  .product-features ul {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .products {
    padding: 40px 0 0 0;
  }

  #product-heading {
    font-size: 3rem;
  }

  #product-subtittle {
    font-size: 1.2rem;
    padding: 0 20px;
  }

  .products-carousel {
    height: calc(100vh - 150px);
  }

  .single-product {
    flex-direction: column;
    text-align: center;
    padding: 30px 20px;
    justify-content: flex-start;
    overflow-y: auto;
  }

  .product-image-container {
    flex: none;
    margin-right: 0;
    margin-bottom: 30px;
    width: 100%;
    max-width: 350px;
  }

  .product-image-container img {
    height: 280px;
  }

  .product-info {
    max-width: 100%;
    padding: 0;
  }

  .product-name {
    font-size: 1.8rem;
  }

  .current-price {
    font-size: 1.5rem;
  }

  .product-features ul {
    grid-template-columns: 1fr;
  }

  .product-actions {
    flex-direction: column;
  }

  .control-btn {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  .specs-table td {
    padding: 8px 12px;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .single-product {
    padding: 20px 15px;
  }

  .product-image-container img {
    height: 220px;
  }

  .product-name {
    font-size: 1.5rem;
  }

  .current-price {
    font-size: 1.3rem;
  }

  .product-controls {
    gap: 10px;
  }

  .control-btn {
    width: 35px;
    height: 35px;
    font-size: 14px;
  }
}

/*====================Products end=====================================================*/