@font-face {
  font-family: 'Boldivia';
  src: url('../src/assets/fonts/boldovia.regular.ttf') format('truetype');
  font-style: normal;
  font-weight: normal;
}

.middle-background {
  position: relative;
  width: 100%;
  height: 100%;
  left: 0;
  z-index: 0;
  background: rgb(0, 0, 0);
  min-height: 100vh;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
}


.parent {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-auto-rows: minmax(200px, auto);
  gap: 20px;
  padding: 20px;
  background: rgb(0, 0, 0);
  max-height: 500px;
}

/* Grid Item Sizes to Match Screenshot */
.div1 {
  grid-row: span 2;
  cursor: pointer;
}

.div2 {
  grid-column: 2;
  grid-row: 1;
  cursor: pointer;
}

.div3 {
  grid-column: 2;
  grid-row: 2;
  cursor: pointer;
}

.div4 {
  grid-column: 3;
  grid-row: 1;
  cursor: pointer;
}

.div5 {
  grid-column: 3;
  grid-row: 2;
  cursor: pointer;
}

.div1:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.div2:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.div3:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.div4:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.div5:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

/* Optional: Media Styling */
img,
video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
}

/*===========================Marquee start=================================================================*/
.marquee-tilted-wrapper {
  overflow: hidden;
  background: rgb(0, 0, 0);
}

.marquee-wrapper {
  overflow: hidden;
  width: 100%;
}

.marquee-content {
  display: flex;
  gap: 4rem;
  min-width: 100%;
  white-space: nowrap;
  animation: scrollMarquee 15s linear infinite;
}

.marquee-content span {
  font-size: 2rem;
  font-weight: bold;
  color: #d35400;
}

/* Keyframes for infinite scroll */
@keyframes scrollMarquee {
  0% {
    transform: translateX(100%);
  }

  100% {
    transform: translateX(-150%);
  }
}

/*===========================Marquee end=================================================================*/

.banner {
  display: flex;
  position: relative;
  justify-content: center;
  align-items: center;
  border-radius: 0;
  width: 100%;
  object-fit: fill;
  object-position: center;
}

#Banner1 {
  width: 100%;
  height: 100%;
  border-radius: 0%;
  object-fit: fill;
  object-position: center;
}


#Banner1 {
  width: 100%;
  height: 600px;
  display: block;
}

#banner-heading {
  position: absolute;
  font-size: clamp(2.5rem, 6vw, 4.5rem);
  top: 15%;
  left: 25%;
  transform: translate(-50%, -50%);
  font-family: 'boldivia';
  color: rgb(255, 255, 255);
  font-weight: bold;

  pointer-events: none;
}

#banner-subtittle {
  position: absolute;
  font-size: clamp(1.125rem, 3vw, 1.75rem);
  top: 32%;
  left: 25%;
  transform: translate(-50%, -50%);
  font-family: 'boldivia';
  color: rgb(255, 255, 255);
  font-weight: bold;

  pointer-events: none;
}

#banner-button {
  position: absolute;
  top: 50%;
  left: 20%;

  font-size: 1.25rem;
  font-weight: bold;
  cursor: pointer;
}

/* From Uiverse.io by mrhyddenn */ 
button {
  background: #fff;
  border: none;
  padding: 10px 10px;
  display: inline-block;
  font-size: 15px;
  font-weight: 600;
  width: 120px;
  text-transform: uppercase;
  cursor: pointer;
  transform: skew(-21deg);
}

#button-text {
  display: inline-block;
  transform: skew(21deg);
}

button::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  right: 100%;
  left: 0;
  background: rgb(20, 20, 20);
  opacity: 0;
  z-index: -1;
  transition: all 0.5s;
}

button:hover {
  color: #fff;
}

button:hover::before {
  left: 0;
  right: 0;
  opacity: 1;
}




/*====================Trending Now start=====================================================*/

.headings {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-family: "boldivia";
  background: rgb(0, 0, 0);
}

#main-heading {
  color: #ffffff;
  font-size: 5rem;
  margin-top: 0rem;
}

#subtittle {
  color: rgb(255, 255, 255);
  font-size: 1rem;
  margin-top: -4rem;
}


.trending-images {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-template-rows: repeat(4, 1fr);
  gap: 20px;
  padding: 20px;
  background: rgb(0, 0, 0);
  max-height: 380px;
}

.trend1 {
  grid-row: span 4 / span 4;
  height: 350px;
  width: 280px;
  cursor: pointer;
}

.trend2 {
  grid-row: span 4 / span 4;
  height: 350px;
  width: 280px;
  cursor: pointer;
}

.trend3 {
  grid-row: span 4 / span 4;
  height: 350px;
  width: 280px;
  cursor: pointer;
}

.trend4 {
  grid-row: span 4 / span 4;
  height: 350px;
  width: 280px;
  cursor: pointer;
}

.trend5 {
  grid-row: span 4 / span 4;
  height: 350px;
  width: 280px;
  cursor: pointer;
}

.trend1:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.trend2:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.trend3:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.trend4:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.trend5:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}



/*====================Trending Now end=====================================================*/


/*====================Premium Products Showcase=====================================================*/

.premium-showcase {
  width: 100vw;
  min-height: 100vh;
  background: #000000;
  font-family: "boldivia", sans-serif;
  position: relative;
  overflow: hidden;
}

.showcase-header {
  text-align: center;
  padding: 80px 20px 60px;
  position: relative;
  z-index: 10;
}

.showcase-title {
  font-size: 6rem;
  font-weight: 900;
  color: #ffffff;
  margin: 0;
  text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
  letter-spacing: 3px;
  background: linear-gradient(45deg, #ffffff, #cccccc);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.showcase-subtitle {
  font-size: 1.8rem;
  color: #888888;
  margin: 20px 0 0 0;
  font-weight: 300;
  letter-spacing: 2px;
  text-transform: uppercase;
}

.product-display-area {
  width: 100%;
  height: calc(100vh - 300px);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 40px;
}

.product-card {
  display: flex;
  width: 100%;
  max-width: 1400px;
  height: 100%;
  border-radius: 25px;
  overflow: hidden;
  position: relative;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  animation: slideIn 0.8s ease-out forwards;
  opacity: 0;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.product-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.4);
}

/* Dynamic Background Themes */
.product-card.product1 {
  background: linear-gradient(135deg, #1a0d2e, #16213e, #0f3460);
  box-shadow: 0 20px 40px rgba(26, 13, 46, 0.4);
}

.product-card.product2 {
  background: linear-gradient(135deg, #0f2027, #203a43, #2c5364);
  box-shadow: 0 20px 40px rgba(15, 32, 39, 0.4);
}

.product-card.product3 {
  background: linear-gradient(135deg, #134e5e, #71b280, #a8e6cf);
  box-shadow: 0 20px 40px rgba(19, 78, 94, 0.4);
}

.product-card.product4 {
  background: linear-gradient(135deg, #2c1810, #8b4513, #cd853f);
  box-shadow: 0 20px 40px rgba(44, 24, 16, 0.4);
}

.product-card.product5 {
  background: linear-gradient(135deg, #4a148c, #7b1fa2, #9c27b0);
  box-shadow: 0 20px 40px rgba(74, 20, 140, 0.4);
}

/* Product Visual Section (Left Side) */
.product-visual {
  flex: 0 0 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 40px;
  position: relative;
}

.image-frame {
  position: relative;
  width: 100%;
  max-width: 450px;
  height: 450px;
  border-radius: 30px;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
  border: 4px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.6s ease;
}

.product-image:hover {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 60%, rgba(255, 255, 255, 0.1));
  pointer-events: none;
}

/* Navigation Controls */
.navigation-controls {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 40px;
}

.nav-dot {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: 3px solid rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(15px);
  position: relative;
  overflow: hidden;
}

.nav-dot::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.nav-dot:hover::before {
  left: 100%;
}

.nav-dot:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: scale(1.1) translateY(-5px);
  box-shadow: 0 10px 25px rgba(255, 255, 255, 0.2);
}

.nav-dot.active {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.6);
  box-shadow: 0 15px 30px rgba(255, 255, 255, 0.3);
  transform: scale(1.15);
}

/* Product Details Section (Right Side) */
.product-details {
  flex: 1;
  padding: 60px 50px;
  color: white;
  overflow-y: auto;
  max-height: 100%;
}

/* Detail Header */
.detail-header {
  margin-bottom: 40px;
  padding-bottom: 30px;
  border-bottom: 2px solid rgba(255, 255, 255, 0.15);
  position: relative;
}

.detail-header::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 80px;
  height: 2px;
  background: linear-gradient(90deg, #00bcd4, #4caf50);
}

.product-title {
  font-size: 3rem;
  font-weight: 900;
  margin: 0 0 15px 0;
  color: #ffffff;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.7);
  line-height: 1.2;
  background: linear-gradient(45deg, #ffffff, #e0e0e0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.product-code {
  font-size: 1.1rem;
  color: #888888;
  margin-bottom: 25px;
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
}

.price-section {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.price-current {
  font-size: 2.5rem;
  font-weight: 900;
  color: #00e676;
  text-shadow: 0 0 10px rgba(0, 230, 118, 0.3);
}

.price-original {
  font-size: 1.4rem;
  color: #666666;
  text-decoration: line-through;
  opacity: 0.7;
}

.discount-badge {
  background: linear-gradient(45deg, #ff4444, #ff6b6b);
  color: white;
  padding: 8px 16px;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: bold;
  box-shadow: 0 4px 15px rgba(255, 68, 68, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.stock-status {
  background: rgba(0, 230, 118, 0.15);
  color: #00e676;
  padding: 8px 20px;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: bold;
  border: 1px solid rgba(0, 230, 118, 0.3);
  backdrop-filter: blur(10px);
}

.rating-display {
  color: #ffd700;
  font-size: 1.2rem;
  font-weight: bold;
  text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

/* Section Titles */
.section-title {
  font-size: 1.6rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 20px 0;
  position: relative;
  padding-left: 20px;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, #00bcd4, #4caf50);
  border-radius: 2px;
}

/* Description Block */
.description-block {
  margin-bottom: 35px;
}

.description-text {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #cccccc;
  margin: 0;
  text-align: justify;
}

/* Features Grid */
.features-grid {
  margin-bottom: 35px;
}

.features-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.05);
  padding: 15px 20px;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.feature-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(0, 188, 212, 0.5);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 188, 212, 0.2);
}

.feature-icon {
  color: #00bcd4;
  font-size: 1.2rem;
  font-weight: bold;
  flex-shrink: 0;
}

.feature-text {
  color: #e0e0e0;
  font-size: 0.95rem;
  font-weight: 500;
}

/* Specifications Section */
.specs-section {
  margin-bottom: 40px;
}

.specs-grid {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 15px;
  padding: 25px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
}

.spec-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  transition: all 0.3s ease;
}

.spec-row:last-child {
  border-bottom: none;
}

.spec-row:hover {
  background: rgba(255, 255, 255, 0.05);
  margin: 0 -15px;
  padding-left: 15px;
  padding-right: 15px;
  border-radius: 8px;
}

.spec-label {
  font-weight: 600;
  color: #ffffff;
  font-size: 0.95rem;
}

.spec-value {
  color: #00bcd4;
  font-weight: 500;
  font-size: 0.95rem;
  text-align: right;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 20px;
  margin-top: 40px;
}

.btn-primary {
  flex: 2;
  background: linear-gradient(135deg, #00bcd4, #4caf50);
  color: white;
  border: none;
  padding: 18px 30px;
  font-size: 1.1rem;
  font-weight: 700;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  box-shadow: 0 8px 25px rgba(0, 188, 212, 0.3);
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #4caf50, #00bcd4);
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 188, 212, 0.4);
}

.btn-secondary {
  flex: 1;
  background: rgba(255, 255, 255, 0.08);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.2);
  padding: 18px 25px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  backdrop-filter: blur(15px);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(255, 255, 255, 0.1);
}

.btn-icon {
  font-size: 1.2rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .showcase-title {
    font-size: 4.5rem;
  }

  .product-display-area {
    padding: 0 30px;
  }

  .product-visual {
    padding: 40px 30px;
  }

  .product-details {
    padding: 40px 30px;
  }

  .image-frame {
    max-width: 380px;
    height: 380px;
  }

  .product-title {
    font-size: 2.5rem;
  }
}

@media (max-width: 768px) {
  .showcase-header {
    padding: 60px 20px 40px;
  }

  .showcase-title {
    font-size: 3.5rem;
  }

  .showcase-subtitle {
    font-size: 1.4rem;
  }

  .product-display-area {
    height: auto;
    min-height: calc(100vh - 200px);
    padding: 0 20px;
  }

  .product-card {
    flex-direction: column;
    max-width: 100%;
  }

  .product-visual {
    flex: none;
    padding: 30px 20px;
  }

  .image-frame {
    max-width: 320px;
    height: 320px;
  }

  .nav-dot {
    width: 50px;
    height: 50px;
    font-size: 16px;
  }

  .navigation-controls {
    gap: 15px;
    margin-top: 30px;
  }

  .product-details {
    padding: 30px 20px;
  }

  .product-title {
    font-size: 2rem;
    text-align: center;
  }

  .price-current {
    font-size: 2rem;
  }

  .features-list {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    flex-direction: column;
  }

  .btn-primary,
  .btn-secondary {
    flex: none;
  }
}

@media (max-width: 480px) {
  .showcase-title {
    font-size: 2.5rem;
  }

  .showcase-subtitle {
    font-size: 1.1rem;
  }

  .product-display-area {
    padding: 0 15px;
  }

  .product-visual {
    padding: 20px 15px;
  }

  .product-details {
    padding: 20px 15px;
  }

  .image-frame {
    max-width: 280px;
    height: 280px;
  }

  .product-title {
    font-size: 1.6rem;
  }

  .price-current {
    font-size: 1.8rem;
  }

  .nav-dot {
    width: 45px;
    height: 45px;
    font-size: 14px;
  }

  .navigation-controls {
    gap: 12px;
  }

  .feature-item {
    padding: 12px 15px;
  }

  .spec-row {
    padding: 12px 0;
  }

  .btn-primary,
  .btn-secondary {
    padding: 15px 20px;
    font-size: 1rem;
  }
}

/*====================Premium Products Showcase End=====================================================*/