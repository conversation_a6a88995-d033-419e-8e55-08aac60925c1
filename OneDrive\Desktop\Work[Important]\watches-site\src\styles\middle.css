@font-face {
  font-family: 'Boldivia';
  src: url('../src/assets/fonts/boldovia.regular.ttf') format('truetype');
  font-style: normal;
  font-weight: normal;
}

.middle-background {
  position: relative;
  width: 100%;
  height: 100%;
  left: 0;
  z-index: 0;
  background: rgb(0, 0, 0);
  min-height: 100vh;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
}


.parent {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-auto-rows: minmax(200px, auto);
  gap: 20px;
  padding: 20px;
  background: rgb(0, 0, 0);
  max-height: 500px;
}

/* Grid Item Sizes to Match Screenshot */
.div1 {
  grid-row: span 2;
  cursor: pointer;
}

.div2 {
  grid-column: 2;
  grid-row: 1;
  cursor: pointer;
}

.div3 {
  grid-column: 2;
  grid-row: 2;
  cursor: pointer;
}

.div4 {
  grid-column: 3;
  grid-row: 1;
  cursor: pointer;
}

.div5 {
  grid-column: 3;
  grid-row: 2;
  cursor: pointer;
}

.div1:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.div2:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.div3:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.div4:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.div5:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

/* Optional: Media Styling */
img,
video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
}

/*===========================Marquee start=================================================================*/
.marquee-tilted-wrapper {
  overflow: hidden;
  background: rgb(0, 0, 0);
}

.marquee-wrapper {
  overflow: hidden;
  width: 100%;
}

.marquee-content {
  display: flex;
  gap: 4rem;
  min-width: 100%;
  white-space: nowrap;
  animation: scrollMarquee 15s linear infinite;
}

.marquee-content span {
  font-size: 2rem;
  font-weight: bold;
  color: #d35400;
}

/* Keyframes for infinite scroll */
@keyframes scrollMarquee {
  0% {
    transform: translateX(100%);
  }

  100% {
    transform: translateX(-150%);
  }
}

/*===========================Marquee end=================================================================*/

.banner {
  display: flex;
  position: relative;
  justify-content: center;
  align-items: center;
  border-radius: 0;
  width: 100%;
  object-fit: fill;
  object-position: center;
}

#Banner1 {
  width: 100%;
  height: 100%;
  border-radius: 0%;
  object-fit: fill;
  object-position: center;
}


#Banner1 {
  width: 100%;
  height: 600px;
  display: block;
}

#banner-heading {
  position: absolute;
  font-size: clamp(2.5rem, 6vw, 4.5rem);
  top: 15%;
  left: 25%;
  transform: translate(-50%, -50%);
  font-family: 'boldivia';
  color: rgb(255, 255, 255);
  font-weight: bold;

  pointer-events: none;
}

#banner-subtittle {
  position: absolute;
  font-size: clamp(1.125rem, 3vw, 1.75rem);
  top: 32%;
  left: 25%;
  transform: translate(-50%, -50%);
  font-family: 'boldivia';
  color: rgb(255, 255, 255);
  font-weight: bold;

  pointer-events: none;
}

#banner-button {
  position: absolute;
  top: 50%;
  left: 20%;

  font-size: 1.25rem;
  font-weight: bold;
  cursor: pointer;
}

/* From Uiverse.io by mrhyddenn */ 
button {
  background: #fff;
  border: none;
  padding: 10px 10px;
  display: inline-block;
  font-size: 15px;
  font-weight: 600;
  width: 120px;
  text-transform: uppercase;
  cursor: pointer;
  transform: skew(-21deg);
}

#button-text {
  display: inline-block;
  transform: skew(21deg);
}

button::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  right: 100%;
  left: 0;
  background: rgb(20, 20, 20);
  opacity: 0;
  z-index: -1;
  transition: all 0.5s;
}

button:hover {
  color: #fff;
}

button:hover::before {
  left: 0;
  right: 0;
  opacity: 1;
}




/*====================Trending Now start=====================================================*/

.headings {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-family: "boldivia";
  background: rgb(0, 0, 0);
}

#main-heading {
  color: #ffffff;
  font-size: 5rem;
  margin-top: 0rem;
}

#subtittle {
  color: rgb(255, 255, 255);
  font-size: 1rem;
  margin-top: -4rem;
}


.trending-images {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-template-rows: repeat(4, 1fr);
  gap: 20px;
  padding: 20px;
  background: rgb(0, 0, 0);
  max-height: 380px;
}

.trend1 {
  grid-row: span 4 / span 4;
  height: 350px;
  width: 280px;
  cursor: pointer;
}

.trend2 {
  grid-row: span 4 / span 4;
  height: 350px;
  width: 280px;
  cursor: pointer;
}

.trend3 {
  grid-row: span 4 / span 4;
  height: 350px;
  width: 280px;
  cursor: pointer;
}

.trend4 {
  grid-row: span 4 / span 4;
  height: 350px;
  width: 280px;
  cursor: pointer;
}

.trend5 {
  grid-row: span 4 / span 4;
  height: 350px;
  width: 280px;
  cursor: pointer;
}

.trend1:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.trend2:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.trend3:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.trend4:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.trend5:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}



/*====================Trending Now end=====================================================*/


/*====================Products start=====================================================*/

.products {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-family: "boldivia";
  color: white;
  background: rgb(0, 0, 0);
}
/*====================Products end=====================================================*/